/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */
/**
 * @param {State} state
 * @returns {Exclude<Options['ruleRepetition'], null | undefined>}
 */
export function checkRuleRepetition(state: State): Exclude<Options["ruleRepetition"], null | undefined>;
import type { State } from 'mdast-util-to-markdown';
import type { Options } from 'mdast-util-to-markdown';
//# sourceMappingURL=check-rule-repetition.d.ts.map