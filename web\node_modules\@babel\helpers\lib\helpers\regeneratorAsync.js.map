{"version": 3, "names": ["_regeneratorAsyncGen", "require", "_regeneratorAsync", "innerFn", "outerFn", "self", "tryLocsList", "PromiseImpl", "iter", "asyncGen", "next", "then", "result", "done", "value"], "sources": ["../../src/helpers/regeneratorAsync.ts"], "sourcesContent": ["/* @minVersion 7.27.0 */\n\nimport asyncGen from \"./regeneratorAsyncGen.ts\";\n\nexport default function _regeneratorAsync(\n  innerFn: Function,\n  outerFn: Function,\n  self: any,\n  tryLocsList: any[],\n  PromiseImpl: PromiseConstructor | undefined,\n) {\n  var iter = asyncGen(innerFn, outerFn, self, tryLocsList, PromiseImpl);\n  return iter.next().then(function (result: IteratorResult<any>) {\n    return result.done ? result.value : iter.next();\n  });\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,oBAAA,GAAAC,OAAA;AAEe,SAASC,iBAAiBA,CACvCC,OAAiB,EACjBC,OAAiB,EACjBC,IAAS,EACTC,WAAkB,EAClBC,WAA2C,EAC3C;EACA,IAAIC,IAAI,GAAG,IAAAC,4BAAQ,EAACN,OAAO,EAAEC,OAAO,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,CAAC;EACrE,OAAOC,IAAI,CAACE,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,MAA2B,EAAE;IAC7D,OAAOA,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,GAAGN,IAAI,CAACE,IAAI,CAAC,CAAC;EACjD,CAAC,CAAC;AACJ", "ignoreList": []}