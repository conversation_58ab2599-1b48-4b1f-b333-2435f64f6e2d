export default {
    "code[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#24242e",
        "color": "#767693"
    },
    "pre[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#24242e",
        "color": "#767693",
        "padding": "1em",
        "margin": ".5em 0",
        "overflow": "auto"
    },
    "pre > code[class*=\"language-\"]": {
        "fontSize": "1em"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#5151e6"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#5151e6"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#5151e6"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#5151e6"
    },
    "pre[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#5151e6"
    },
    "pre[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#5151e6"
    },
    "code[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#5151e6"
    },
    "code[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#5151e6"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "padding": ".1em",
        "borderRadius": ".3em"
    },
    "comment": {
        "color": "#5b5b76"
    },
    "prolog": {
        "color": "#5b5b76"
    },
    "doctype": {
        "color": "#5b5b76"
    },
    "cdata": {
        "color": "#5b5b76"
    },
    "punctuation": {
        "color": "#5b5b76"
    },
    "namespace": {
        "Opacity": ".7"
    },
    "tag": {
        "color": "#dd672c"
    },
    "operator": {
        "color": "#dd672c"
    },
    "number": {
        "color": "#dd672c"
    },
    "property": {
        "color": "#767693"
    },
    "function": {
        "color": "#767693"
    },
    "tag-id": {
        "color": "#ebebff"
    },
    "selector": {
        "color": "#ebebff"
    },
    "atrule-id": {
        "color": "#ebebff"
    },
    "code.language-javascript": {
        "color": "#aaaaca"
    },
    "attr-name": {
        "color": "#aaaaca"
    },
    "code.language-css": {
        "color": "#fe8c52"
    },
    "code.language-scss": {
        "color": "#fe8c52"
    },
    "boolean": {
        "color": "#fe8c52"
    },
    "string": {
        "color": "#fe8c52"
    },
    "entity": {
        "color": "#fe8c52",
        "cursor": "help"
    },
    "url": {
        "color": "#fe8c52"
    },
    ".language-css .token.string": {
        "color": "#fe8c52"
    },
    ".language-scss .token.string": {
        "color": "#fe8c52"
    },
    ".style .token.string": {
        "color": "#fe8c52"
    },
    "attr-value": {
        "color": "#fe8c52"
    },
    "keyword": {
        "color": "#fe8c52"
    },
    "control": {
        "color": "#fe8c52"
    },
    "directive": {
        "color": "#fe8c52"
    },
    "unit": {
        "color": "#fe8c52"
    },
    "statement": {
        "color": "#fe8c52"
    },
    "regex": {
        "color": "#fe8c52"
    },
    "atrule": {
        "color": "#fe8c52"
    },
    "placeholder": {
        "color": "#fe8c52"
    },
    "variable": {
        "color": "#fe8c52"
    },
    "deleted": {
        "textDecoration": "line-through"
    },
    "inserted": {
        "borderBottom": "1px dotted #ebebff",
        "textDecoration": "none"
    },
    "italic": {
        "fontStyle": "italic"
    },
    "important": {
        "fontWeight": "bold",
        "color": "#aaaaca"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "pre > code.highlight": {
        "Outline": ".4em solid #7676f4",
        "OutlineOffset": ".4em"
    },
    ".line-numbers.line-numbers .line-numbers-rows": {
        "borderRightColor": "#262631"
    },
    ".line-numbers .line-numbers-rows > span:before": {
        "color": "#393949"
    },
    ".line-highlight.line-highlight": {
        "background": "linear-gradient(to right, rgba(221, 103, 44, 0.2) 70%, rgba(221, 103, 44, 0))"
    }
}