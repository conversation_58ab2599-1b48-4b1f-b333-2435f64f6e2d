import base64
import os
from pdf2image import convert_from_path
from openai import OpenAI
import tempfile
import prompt
import time

# 设置OpenAI客户端
openai_api_key = "token-abc123"
openai_api_base = "http://192.168.0.7:8006/v1"
client = OpenAI(api_key=openai_api_key, base_url=openai_api_base)

# PDF文件路径
pdf_path = 'D:/Data/仲裁卷宗/2024-1177.pdf'  # 替换为你的PDF路径

# 设置要识别的页码范围 (1-based索引)
start_page = 1  # 起始页码
end_page = 100  # 结束页码 (设置为None可处理到最后一页)

# 1. 将PDF转换为图像列表 (只加载需要的页面)
if end_page is None:
    images = convert_from_path(pdf_path, dpi=400)
else:
    images = convert_from_path(pdf_path, dpi=400, first_page=start_page, last_page=end_page)

# 2. 准备结果存储
all_results = []
temp_files = []  # 用于保存临时文件路径

print(f"正在处理 {len(images)} 页文档...")

# 3. 逐页处理
for page_index, image in enumerate(images):
    current_page = start_page + page_index
    print(f"\n正在处理第 {current_page} 页...")

    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
        temp_path = temp_file.name
        temp_files.append(temp_path)

    # 保存并编码图像
    image.save(temp_path, 'PNG')
    with open(temp_path, 'rb') as file:
        base64_image = base64.b64encode(file.read()).decode('utf-8')
        image_data = f"data:image/png;base64,{base64_image}"

    # 构建当前页的消息
    messages = [{
        "role": "user",
        "content": [
            {"type": "text", "text": f"{prompt.prompt_auto}"},
            {"type": "image_url", "image_url": {"url": image_data}}
        ]
    }]

    # 4. 发送当前页的请求
    print(f"正在发送第 {current_page} 页的请求到API...")
    try:
        chat_response = client.chat.completions.create(
            model="Qwen2.5-VL-7B-Instruct",
            messages=messages,
            extra_body={"stop_token_ids": [151645, 151643]},
            timeout=60  # 增加超时时间
        )

        # 获取当前页的结果
        page_result = chat_response.choices[0].message.content
        all_results.append((current_page, page_result))

        # 输出当前页结果
        print(f"\n第 {current_page} 页分析结果:")
        print(page_result)

    except Exception as e:
        error_msg = f"第 {current_page} 页处理出错: {str(e)}"
        print(error_msg)
        all_results.append((current_page, error_msg))

    # 添加延迟避免请求过载
    time.sleep(1)

# 5. 输出所有结果
print("\n\n===== 所有页面分析结果 =====")
for page_num, result in all_results:
    print(f"\n=== 第 {page_num} 页结果 ===")
    print(result)

# 6. 清理临时文件
print("\n清理临时文件...")
for temp_path in temp_files:
    try:
        os.remove(temp_path)
        print(f"已删除: {temp_path}")
    except Exception as e:
        print(f"删除文件 {temp_path} 时出错: {str(e)}")

print("处理完成！")