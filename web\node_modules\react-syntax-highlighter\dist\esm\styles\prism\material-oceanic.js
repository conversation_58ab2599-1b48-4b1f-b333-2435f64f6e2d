export default {
  "code[class*=\"language-\"]": {
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "color": "#c3cee3",
    "background": "#263238",
    "fontFamily": "Roboto Mono, monospace",
    "fontSize": "1em",
    "lineHeight": "1.5em",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "color": "#c3cee3",
    "background": "#263238",
    "fontFamily": "Roboto Mono, monospace",
    "fontSize": "1em",
    "lineHeight": "1.5em",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "overflow": "auto",
    "position": "relative",
    "margin": "0.5em 0",
    "padding": "1.25em 1em"
  },
  "code[class*=\"language-\"]::-moz-selection": {
    "background": "#363636"
  },
  "pre[class*=\"language-\"]::-moz-selection": {
    "background": "#363636"
  },
  "code[class*=\"language-\"] ::-moz-selection": {
    "background": "#363636"
  },
  "pre[class*=\"language-\"] ::-moz-selection": {
    "background": "#363636"
  },
  "code[class*=\"language-\"]::selection": {
    "background": "#363636"
  },
  "pre[class*=\"language-\"]::selection": {
    "background": "#363636"
  },
  "code[class*=\"language-\"] ::selection": {
    "background": "#363636"
  },
  "pre[class*=\"language-\"] ::selection": {
    "background": "#363636"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "whiteSpace": "normal",
    "borderRadius": "0.2em",
    "padding": "0.1em"
  },
  ".language-css > code": {
    "color": "#fd9170"
  },
  ".language-sass > code": {
    "color": "#fd9170"
  },
  ".language-scss > code": {
    "color": "#fd9170"
  },
  "[class*=\"language-\"] .namespace": {
    "Opacity": "0.7"
  },
  "atrule": {
    "color": "#c792ea"
  },
  "attr-name": {
    "color": "#ffcb6b"
  },
  "attr-value": {
    "color": "#c3e88d"
  },
  "attribute": {
    "color": "#c3e88d"
  },
  "boolean": {
    "color": "#c792ea"
  },
  "builtin": {
    "color": "#ffcb6b"
  },
  "cdata": {
    "color": "#80cbc4"
  },
  "char": {
    "color": "#80cbc4"
  },
  "class": {
    "color": "#ffcb6b"
  },
  "class-name": {
    "color": "#f2ff00"
  },
  "color": {
    "color": "#f2ff00"
  },
  "comment": {
    "color": "#546e7a"
  },
  "constant": {
    "color": "#c792ea"
  },
  "deleted": {
    "color": "#f07178"
  },
  "doctype": {
    "color": "#546e7a"
  },
  "entity": {
    "color": "#f07178"
  },
  "function": {
    "color": "#c792ea"
  },
  "hexcode": {
    "color": "#f2ff00"
  },
  "id": {
    "color": "#c792ea",
    "fontWeight": "bold"
  },
  "important": {
    "color": "#c792ea",
    "fontWeight": "bold"
  },
  "inserted": {
    "color": "#80cbc4"
  },
  "keyword": {
    "color": "#c792ea",
    "fontStyle": "italic"
  },
  "number": {
    "color": "#fd9170"
  },
  "operator": {
    "color": "#89ddff"
  },
  "prolog": {
    "color": "#546e7a"
  },
  "property": {
    "color": "#80cbc4"
  },
  "pseudo-class": {
    "color": "#c3e88d"
  },
  "pseudo-element": {
    "color": "#c3e88d"
  },
  "punctuation": {
    "color": "#89ddff"
  },
  "regex": {
    "color": "#f2ff00"
  },
  "selector": {
    "color": "#f07178"
  },
  "string": {
    "color": "#c3e88d"
  },
  "symbol": {
    "color": "#c792ea"
  },
  "tag": {
    "color": "#f07178"
  },
  "unit": {
    "color": "#f07178"
  },
  "url": {
    "color": "#fd9170"
  },
  "variable": {
    "color": "#f07178"
  }
};