export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#eaeef3",
    "color": "#00193a"
  },
  "hljs-keyword": {
    "fontWeight": "bold"
  },
  "hljs-selector-tag": {
    "fontWeight": "bold"
  },
  "hljs-title": {
    "fontWeight": "bold",
    "color": "#0048ab"
  },
  "hljs-section": {
    "fontWeight": "bold",
    "color": "#0048ab"
  },
  "hljs-doctag": {
    "fontWeight": "bold"
  },
  "hljs-name": {
    "fontWeight": "bold",
    "color": "#0048ab"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  },
  "hljs-comment": {
    "color": "#738191"
  },
  "hljs-string": {
    "color": "#0048ab"
  },
  "hljs-built_in": {
    "color": "#0048ab"
  },
  "hljs-literal": {
    "color": "#0048ab"
  },
  "hljs-type": {
    "color": "#0048ab"
  },
  "hljs-addition": {
    "color": "#0048ab"
  },
  "hljs-tag": {
    "color": "#0048ab"
  },
  "hljs-quote": {
    "color": "#0048ab"
  },
  "hljs-selector-id": {
    "color": "#0048ab"
  },
  "hljs-selector-class": {
    "color": "#0048ab"
  },
  "hljs-meta": {
    "color": "#4c81c9"
  },
  "hljs-subst": {
    "color": "#4c81c9"
  },
  "hljs-symbol": {
    "color": "#4c81c9"
  },
  "hljs-regexp": {
    "color": "#4c81c9"
  },
  "hljs-attribute": {
    "color": "#4c81c9"
  },
  "hljs-deletion": {
    "color": "#4c81c9"
  },
  "hljs-variable": {
    "color": "#4c81c9"
  },
  "hljs-template-variable": {
    "color": "#4c81c9"
  },
  "hljs-link": {
    "color": "#4c81c9"
  },
  "hljs-bullet": {
    "color": "#4c81c9"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  }
};