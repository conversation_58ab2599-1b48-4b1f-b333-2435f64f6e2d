export default {
  "hljs-comment": {
    "color": "#898ea4"
  },
  "hljs-quote": {
    "color": "#898ea4"
  },
  "hljs-variable": {
    "color": "#c94922"
  },
  "hljs-template-variable": {
    "color": "#c94922"
  },
  "hljs-attribute": {
    "color": "#c94922"
  },
  "hljs-tag": {
    "color": "#c94922"
  },
  "hljs-name": {
    "color": "#c94922"
  },
  "hljs-regexp": {
    "color": "#c94922"
  },
  "hljs-link": {
    "color": "#c94922"
  },
  "hljs-selector-id": {
    "color": "#c94922"
  },
  "hljs-selector-class": {
    "color": "#c94922"
  },
  "hljs-number": {
    "color": "#c76b29"
  },
  "hljs-meta": {
    "color": "#c76b29"
  },
  "hljs-built_in": {
    "color": "#c76b29"
  },
  "hljs-builtin-name": {
    "color": "#c76b29"
  },
  "hljs-literal": {
    "color": "#c76b29"
  },
  "hljs-type": {
    "color": "#c76b29"
  },
  "hljs-params": {
    "color": "#c76b29"
  },
  "hljs-string": {
    "color": "#ac9739"
  },
  "hljs-symbol": {
    "color": "#ac9739"
  },
  "hljs-bullet": {
    "color": "#ac9739"
  },
  "hljs-title": {
    "color": "#3d8fd1"
  },
  "hljs-section": {
    "color": "#3d8fd1"
  },
  "hljs-keyword": {
    "color": "#6679cc"
  },
  "hljs-selector-tag": {
    "color": "#6679cc"
  },
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "background": "#202746",
    "color": "#979db4",
    "padding": "0.5em"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};