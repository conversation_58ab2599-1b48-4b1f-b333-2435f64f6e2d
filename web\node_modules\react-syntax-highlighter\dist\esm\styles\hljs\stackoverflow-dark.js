export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "color": "#ffffff",
    "background": "#1c1b1b"
  },
  "hljs-comment": {
    "color": "#999999"
  },
  "hljs-keyword": {
    "color": "#88aece"
  },
  "hljs-selector-tag": {
    "color": "#88aece"
  },
  "hljs-meta-keyword": {
    "color": "#88aece"
  },
  "hljs-doctag": {
    "color": "#88aece"
  },
  "hljs-section": {
    "color": "#88aece"
  },
  "hljs-selector-class": {
    "color": "#88aece"
  },
  "hljs-meta": {
    "color": "#88aece"
  },
  "hljs-selector-pseudo": {
    "color": "#88aece"
  },
  "hljs-attr": {
    "color": "#88aece"
  },
  "hljs-attribute": {
    "color": "v#c59bc1"
  },
  "hljs-name": {
    "color": "#f08d49"
  },
  "hljs-type": {
    "color": "#f08d49"
  },
  "hljs-number": {
    "color": "#f08d49"
  },
  "hljs-selector-id": {
    "color": "#f08d49"
  },
  "hljs-quote": {
    "color": "#f08d49"
  },
  "hljs-template-tag": {
    "color": "#f08d49"
  },
  "hljs-built_in": {
    "color": "#f08d49"
  },
  "hljs-title": {
    "color": "#f08d49"
  },
  "hljs-literal": {
    "color": "#f08d49"
  },
  "hljs-string": {
    "color": "#b5bd68"
  },
  "hljs-regexp": {
    "color": "#b5bd68"
  },
  "hljs-symbol": {
    "color": "#b5bd68"
  },
  "hljs-variable": {
    "color": "#b5bd68"
  },
  "hljs-template-variable": {
    "color": "#b5bd68"
  },
  "hljs-link": {
    "color": "#b5bd68"
  },
  "hljs-selector-attr": {
    "color": "#b5bd68"
  },
  "hljs-meta-string": {
    "color": "#b5bd68"
  },
  "hljs-bullet": {
    "color": "#cccccc"
  },
  "hljs-code": {
    "color": "#cccccc"
  },
  "hljs-deletion": {
    "color": "#de7176"
  },
  "hljs-addition": {
    "color": "#76c490"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};