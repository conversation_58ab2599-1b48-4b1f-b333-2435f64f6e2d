export default {
  "hljs": {
    "color": "#a9b7c6",
    "background": "#282b2e",
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em"
  },
  "hljs-number": {
    "color": "#6897BB"
  },
  "hljs-literal": {
    "color": "#6897BB"
  },
  "hljs-symbol": {
    "color": "#6897BB"
  },
  "hljs-bullet": {
    "color": "#6897BB"
  },
  "hljs-keyword": {
    "color": "#cc7832"
  },
  "hljs-selector-tag": {
    "color": "#cc7832"
  },
  "hljs-deletion": {
    "color": "#cc7832"
  },
  "hljs-variable": {
    "color": "#629755"
  },
  "hljs-template-variable": {
    "color": "#629755"
  },
  "hljs-link": {
    "color": "#629755"
  },
  "hljs-comment": {
    "color": "#808080"
  },
  "hljs-quote": {
    "color": "#808080"
  },
  "hljs-meta": {
    "color": "#bbb529"
  },
  "hljs-string": {
    "color": "#6A8759"
  },
  "hljs-attribute": {
    "color": "#6A8759"
  },
  "hljs-addition": {
    "color": "#6A8759"
  },
  "hljs-section": {
    "color": "#ffc66d"
  },
  "hljs-title": {
    "color": "#ffc66d"
  },
  "hljs-type": {
    "color": "#ffc66d"
  },
  "hljs-name": {
    "color": "#e8bf6a"
  },
  "hljs-selector-id": {
    "color": "#e8bf6a"
  },
  "hljs-selector-class": {
    "color": "#e8bf6a"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};