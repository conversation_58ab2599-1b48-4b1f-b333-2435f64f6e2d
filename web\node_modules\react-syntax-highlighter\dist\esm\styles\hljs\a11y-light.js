export default {
  "hljs-comment": {
    "color": "#696969"
  },
  "hljs-quote": {
    "color": "#696969"
  },
  "hljs-variable": {
    "color": "#d91e18"
  },
  "hljs-template-variable": {
    "color": "#d91e18"
  },
  "hljs-tag": {
    "color": "#d91e18"
  },
  "hljs-name": {
    "color": "#d91e18"
  },
  "hljs-selector-id": {
    "color": "#d91e18"
  },
  "hljs-selector-class": {
    "color": "#d91e18"
  },
  "hljs-regexp": {
    "color": "#d91e18"
  },
  "hljs-deletion": {
    "color": "#d91e18"
  },
  "hljs-number": {
    "color": "#aa5d00"
  },
  "hljs-built_in": {
    "color": "#aa5d00"
  },
  "hljs-builtin-name": {
    "color": "#aa5d00"
  },
  "hljs-literal": {
    "color": "#aa5d00"
  },
  "hljs-type": {
    "color": "#aa5d00"
  },
  "hljs-params": {
    "color": "#aa5d00"
  },
  "hljs-meta": {
    "color": "#aa5d00"
  },
  "hljs-link": {
    "color": "#aa5d00"
  },
  "hljs-attribute": {
    "color": "#aa5d00"
  },
  "hljs-string": {
    "color": "#008000"
  },
  "hljs-symbol": {
    "color": "#008000"
  },
  "hljs-bullet": {
    "color": "#008000"
  },
  "hljs-addition": {
    "color": "#008000"
  },
  "hljs-title": {
    "color": "#007faa"
  },
  "hljs-section": {
    "color": "#007faa"
  },
  "hljs-keyword": {
    "color": "#7928a1"
  },
  "hljs-selector-tag": {
    "color": "#7928a1"
  },
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "background": "#fefefe",
    "color": "#545454",
    "padding": "0.5em"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};