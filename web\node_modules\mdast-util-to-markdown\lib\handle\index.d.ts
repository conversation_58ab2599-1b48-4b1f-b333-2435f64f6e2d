export namespace handle {
    export { blockquote };
    export { hardBreak as break };
    export { code };
    export { definition };
    export { emphasis };
    export { hardBreak };
    export { heading };
    export { html };
    export { image };
    export { imageReference };
    export { inlineCode };
    export { link };
    export { linkReference };
    export { list };
    export { listItem };
    export { paragraph };
    export { root };
    export { strong };
    export { text };
    export { thematicBreak };
}
import { blockquote } from './blockquote.js';
import { hardBreak } from './break.js';
import { code } from './code.js';
import { definition } from './definition.js';
import { emphasis } from './emphasis.js';
import { heading } from './heading.js';
import { html } from './html.js';
import { image } from './image.js';
import { imageReference } from './image-reference.js';
import { inlineCode } from './inline-code.js';
import { link } from './link.js';
import { linkReference } from './link-reference.js';
import { list } from './list.js';
import { listItem } from './list-item.js';
import { paragraph } from './paragraph.js';
import { root } from './root.js';
import { strong } from './strong.js';
import { text } from './text.js';
import { thematicBreak } from './thematic-break.js';
//# sourceMappingURL=index.d.ts.map