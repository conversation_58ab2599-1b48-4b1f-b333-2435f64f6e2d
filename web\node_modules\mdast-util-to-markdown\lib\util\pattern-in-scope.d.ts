/**
 * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'
 */
/**
 * @param {Array<ConstructName>} stack
 * @param {Unsafe} pattern
 * @returns {boolean}
 */
export function patternInScope(stack: Array<ConstructName>, pattern: Unsafe): boolean;
import type { ConstructName } from 'mdast-util-to-markdown';
import type { Unsafe } from 'mdast-util-to-markdown';
//# sourceMappingURL=pattern-in-scope.d.ts.map