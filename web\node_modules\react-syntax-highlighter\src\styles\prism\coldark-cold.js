export default {
    "code[class*=\"language-\"]": {
        "color": "#111b27",
        "background": "none",
        "fontFamily": "Consolas, Monaco, \"Andale Mono\", \"Ubuntu Mono\", monospace",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none"
    },
    "pre[class*=\"language-\"]": {
        "color": "#111b27",
        "background": "#e3eaf2",
        "fontFamily": "Consolas, Monaco, \"Andale Mono\", \"Ubuntu Mono\", monospace",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "padding": "1em",
        "margin": "0.5em 0",
        "overflow": "auto"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "background": "#8da1b9"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "background": "#8da1b9"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "background": "#8da1b9"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "background": "#8da1b9"
    },
    "pre[class*=\"language-\"]::selection": {
        "background": "#8da1b9"
    },
    "pre[class*=\"language-\"] ::selection": {
        "background": "#8da1b9"
    },
    "code[class*=\"language-\"]::selection": {
        "background": "#8da1b9"
    },
    "code[class*=\"language-\"] ::selection": {
        "background": "#8da1b9"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "background": "#e3eaf2",
        "padding": "0.1em 0.3em",
        "borderRadius": "0.3em",
        "whiteSpace": "normal"
    },
    "comment": {
        "color": "#3c526d"
    },
    "prolog": {
        "color": "#3c526d"
    },
    "doctype": {
        "color": "#3c526d"
    },
    "cdata": {
        "color": "#3c526d"
    },
    "punctuation": {
        "color": "#111b27"
    },
    "delimiter.important": {
        "color": "#006d6d",
        "fontWeight": "inherit"
    },
    "selector.parent": {
        "color": "#006d6d"
    },
    "tag": {
        "color": "#006d6d"
    },
    "tag.punctuation": {
        "color": "#006d6d"
    },
    "attr-name": {
        "color": "#755f00"
    },
    "boolean": {
        "color": "#755f00"
    },
    "boolean.important": {
        "color": "#755f00"
    },
    "number": {
        "color": "#755f00"
    },
    "constant": {
        "color": "#755f00"
    },
    "selector.attribute": {
        "color": "#755f00"
    },
    "class-name": {
        "color": "#005a8e"
    },
    "key": {
        "color": "#005a8e"
    },
    "parameter": {
        "color": "#005a8e"
    },
    "property": {
        "color": "#005a8e"
    },
    "property-access": {
        "color": "#005a8e"
    },
    "variable": {
        "color": "#005a8e"
    },
    "attr-value": {
        "color": "#116b00"
    },
    "inserted": {
        "color": "#116b00"
    },
    "color": {
        "color": "#116b00"
    },
    "selector.value": {
        "color": "#116b00"
    },
    "string": {
        "color": "#116b00"
    },
    "string.url-link": {
        "color": "#116b00"
    },
    "builtin": {
        "color": "#af00af"
    },
    "keyword-array": {
        "color": "#af00af"
    },
    "package": {
        "color": "#af00af"
    },
    "regex": {
        "color": "#af00af"
    },
    "function": {
        "color": "#7c00aa"
    },
    "selector.class": {
        "color": "#7c00aa"
    },
    "selector.id": {
        "color": "#7c00aa"
    },
    "atrule.rule": {
        "color": "#a04900"
    },
    "combinator": {
        "color": "#a04900"
    },
    "keyword": {
        "color": "#a04900"
    },
    "operator": {
        "color": "#a04900"
    },
    "pseudo-class": {
        "color": "#a04900"
    },
    "pseudo-element": {
        "color": "#a04900"
    },
    "selector": {
        "color": "#a04900"
    },
    "unit": {
        "color": "#a04900"
    },
    "deleted": {
        "color": "#c22f2e"
    },
    "important": {
        "color": "#c22f2e",
        "fontWeight": "bold"
    },
    "keyword-this": {
        "color": "#005a8e",
        "fontWeight": "bold"
    },
    "this": {
        "color": "#005a8e",
        "fontWeight": "bold"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "italic": {
        "fontStyle": "italic"
    },
    "entity": {
        "cursor": "help"
    },
    ".language-markdown .token.title": {
        "color": "#005a8e",
        "fontWeight": "bold"
    },
    ".language-markdown .token.title .token.punctuation": {
        "color": "#005a8e",
        "fontWeight": "bold"
    },
    ".language-markdown .token.blockquote.punctuation": {
        "color": "#af00af"
    },
    ".language-markdown .token.code": {
        "color": "#006d6d"
    },
    ".language-markdown .token.hr.punctuation": {
        "color": "#005a8e"
    },
    ".language-markdown .token.url > .token.content": {
        "color": "#116b00"
    },
    ".language-markdown .token.url-link": {
        "color": "#755f00"
    },
    ".language-markdown .token.list.punctuation": {
        "color": "#af00af"
    },
    ".language-markdown .token.table-header": {
        "color": "#111b27"
    },
    ".language-json .token.operator": {
        "color": "#111b27"
    },
    ".language-scss .token.variable": {
        "color": "#006d6d"
    },
    "token.tab:not(:empty):before": {
        "color": "#3c526d"
    },
    "token.cr:before": {
        "color": "#3c526d"
    },
    "token.lf:before": {
        "color": "#3c526d"
    },
    "token.space:before": {
        "color": "#3c526d"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > a": {
        "color": "#e3eaf2",
        "background": "#005a8e"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > button": {
        "color": "#e3eaf2",
        "background": "#005a8e"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover": {
        "color": "#e3eaf2",
        "background": "#005a8eda",
        "textDecoration": "none"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus": {
        "color": "#e3eaf2",
        "background": "#005a8eda",
        "textDecoration": "none"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover": {
        "color": "#e3eaf2",
        "background": "#005a8eda",
        "textDecoration": "none"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus": {
        "color": "#e3eaf2",
        "background": "#005a8eda",
        "textDecoration": "none"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > span": {
        "color": "#e3eaf2",
        "background": "#3c526d"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover": {
        "color": "#e3eaf2",
        "background": "#3c526d"
    },
    "div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus": {
        "color": "#e3eaf2",
        "background": "#3c526d"
    },
    ".line-highlight.line-highlight": {
        "background": "linear-gradient(to right, #8da1b92f 70%, #8da1b925)"
    },
    ".line-highlight.line-highlight:before": {
        "backgroundColor": "#3c526d",
        "color": "#e3eaf2",
        "boxShadow": "0 1px #8da1b9"
    },
    ".line-highlight.line-highlight[data-end]:after": {
        "backgroundColor": "#3c526d",
        "color": "#e3eaf2",
        "boxShadow": "0 1px #8da1b9"
    },
    "pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before": {
        "backgroundColor": "#3c526d1f"
    },
    ".line-numbers.line-numbers .line-numbers-rows": {
        "borderRight": "1px solid #8da1b97a",
        "background": "#d0dae77a"
    },
    ".line-numbers .line-numbers-rows > span:before": {
        "color": "#3c526dda"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-1": {
        "color": "#755f00"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-5": {
        "color": "#755f00"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-9": {
        "color": "#755f00"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-2": {
        "color": "#af00af"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-6": {
        "color": "#af00af"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-10": {
        "color": "#af00af"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-3": {
        "color": "#005a8e"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-7": {
        "color": "#005a8e"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-11": {
        "color": "#005a8e"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-4": {
        "color": "#7c00aa"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-8": {
        "color": "#7c00aa"
    },
    ".rainbow-braces .token.token.punctuation.brace-level-12": {
        "color": "#7c00aa"
    },
    "pre.diff-highlight > code .token.token.deleted:not(.prefix)": {
        "backgroundColor": "#c22f2e1f"
    },
    "pre > code.diff-highlight .token.token.deleted:not(.prefix)": {
        "backgroundColor": "#c22f2e1f"
    },
    "pre.diff-highlight > code .token.token.inserted:not(.prefix)": {
        "backgroundColor": "#116b001f"
    },
    "pre > code.diff-highlight .token.token.inserted:not(.prefix)": {
        "backgroundColor": "#116b001f"
    },
    ".command-line .command-line-prompt": {
        "borderRight": "1px solid #8da1b97a"
    },
    ".command-line .command-line-prompt > span:before": {
        "color": "#3c526dda"
    }
}