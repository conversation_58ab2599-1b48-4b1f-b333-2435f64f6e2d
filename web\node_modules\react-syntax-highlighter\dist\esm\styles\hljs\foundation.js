export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#eee",
    "color": "black"
  },
  "hljs-link": {
    "color": "#070"
  },
  "hljs-emphasis": {
    "color": "#070",
    "fontStyle": "italic"
  },
  "hljs-attribute": {
    "color": "#070"
  },
  "hljs-addition": {
    "color": "#070"
  },
  "hljs-strong": {
    "color": "#d14",
    "fontWeight": "bold"
  },
  "hljs-string": {
    "color": "#d14"
  },
  "hljs-deletion": {
    "color": "#d14"
  },
  "hljs-quote": {
    "color": "#998",
    "fontStyle": "italic"
  },
  "hljs-comment": {
    "color": "#998",
    "fontStyle": "italic"
  },
  "hljs-section": {
    "color": "#900"
  },
  "hljs-title": {
    "color": "#900"
  },
  "hljs-class .hljs-title": {
    "color": "#458"
  },
  "hljs-type": {
    "color": "#458"
  },
  "hljs-variable": {
    "color": "#336699"
  },
  "hljs-template-variable": {
    "color": "#336699"
  },
  "hljs-bullet": {
    "color": "#997700"
  },
  "hljs-meta": {
    "color": "#3344bb"
  },
  "hljs-code": {
    "color": "#099"
  },
  "hljs-number": {
    "color": "#099"
  },
  "hljs-literal": {
    "color": "#099"
  },
  "hljs-keyword": {
    "color": "#099"
  },
  "hljs-selector-tag": {
    "color": "#099"
  },
  "hljs-regexp": {
    "backgroundColor": "#fff0ff",
    "color": "#880088"
  },
  "hljs-symbol": {
    "color": "#990073"
  },
  "hljs-tag": {
    "color": "#007700"
  },
  "hljs-name": {
    "color": "#007700"
  },
  "hljs-selector-id": {
    "color": "#007700"
  },
  "hljs-selector-class": {
    "color": "#007700"
  }
};