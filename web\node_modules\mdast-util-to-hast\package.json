{"name": "mdast-util-to-hast", "version": "13.2.0", "description": "mdast utility to transform to hast", "license": "MIT", "keywords": ["unist", "mdast", "mdast-util", "hast", "hast-util", "util", "utility", "markdown", "html"], "repository": "syntax-tree/mdast-util-to-hast", "bugs": "https://github.com/syntax-tree/mdast-util-to-hast/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "@ungap/structured-clone": "^1.0.0", "devlop": "^1.0.0", "micromark-util-sanitize-uri": "^2.0.0", "trim-lines": "^3.0.0", "unist-util-position": "^5.0.0", "unist-util-visit": "^5.0.0", "vfile": "^6.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/ungap__structured-clone": "^1.0.0", "c8": "^9.0.0", "hast-util-to-html": "^9.0.0", "hastscript": "^9.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-gfm": "^3.0.0", "micromark-extension-gfm": "^3.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.5.1-rc", "xo": "^0.58.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm", ["remark-lint-no-html", false]]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "#": "needed `any`s", "ignoreFiles": ["lib/state.d.ts"], "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/ban-types": "off", "@typescript-eslint/consistent-type-definitions": "off"}}], "prettier": true, "rules": {"import/no-cycle": "error", "max-depth": "off", "unicorn/prefer-at": "off", "unicorn/prefer-code-point": "off", "unicorn/prefer-string-replace-all": "off"}}}