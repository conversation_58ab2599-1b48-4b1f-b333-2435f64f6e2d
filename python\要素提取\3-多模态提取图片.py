import os
import base64
import pandas as pd
from openai import OpenAI
import prompt


def parse_image_indices(input_str):
    """
    解析图片索引字符串，支持多种格式：
    - 连续范围: "1-10" → [1,2,3,...,10]
    - 离散值: "1,3,5" → [1,3,5]
    - 混合格式: "1,3,5-7" → [1,3,5,6,7]
    - 空值: 返回None表示处理所有图片

    参数:
    input_str: 输入的索引字符串

    返回:
    list: 解析后的整数索引列表，或None（表示所有图片）
    """
    if not input_str:
        return None

    # 如果输入已经是列表，直接返回
    if isinstance(input_str, list):
        return input_str

    # 初始化结果集
    indices = set()

    # 分割不同的部分
    parts = input_str.replace("，", ",").split(',')  # 支持中文逗号

    for part in parts:
        part = part.strip()
        if '-' in part:
            # 处理范围格式
            range_parts = part.split('-')
            if len(range_parts) != 2:
                continue
            try:
                start = int(range_parts[0].strip())
                end = int(range_parts[1].strip())
                # 添加到集合
                indices.update(range(start, end + 1))
            except ValueError:
                continue
        else:
            # 处理单个数字
            try:
                index = int(part)
                indices.add(index)
            except ValueError:
                continue

    return sorted(indices) if indices else None


def process_images_to_excel(folder_path, output_excel, prompt_text,
                            openai_api_key, openai_api_base,
                            image_indices=None, model="gpt-4-vision-preview"):
    """
    处理文件夹中的图片，使用大模型提取信息并保存结果到Excel

    参数:
    folder_path: 包含图片的文件夹路径
    output_excel: 输出的Excel文件路径
    prompt_text: 给模型的提示文本
    openai_api_key: OpenAI API密钥
    openai_api_base: OpenAI API基础URL
    image_indices: 要处理的图片索引字符串或列表(可选)
    model: 使用的模型名称(默认gpt-4-vision-preview)
    """
    # 初始化OpenAI客户端
    client = OpenAI(api_key=openai_api_key, base_url=openai_api_base)

    # 获取图片文件并按数字排序
    image_files = []
    for f in os.listdir(folder_path):
        if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
            # 提取文件名中的数字
            base_name = os.path.splitext(f)[0]
            if base_name.isdigit():
                image_files.append((int(base_name), f))

    # 按数字序号排序
    image_files.sort(key=lambda x: x[0])

    # 解析图片索引
    parsed_indices = parse_image_indices(image_indices)

    # 如果指定了图片序号，则进行筛选
    if parsed_indices is not None:
        image_files = [(idx, f) for idx, f in image_files if idx in parsed_indices]

    # 准备结果存储
    results = []

    # 处理每张图片
    for idx, filename in image_files:
        image_path = os.path.join(folder_path, filename)
        try:
            # 编码图片为base64
            base64_image = encode_image(image_path)

            # 调用模型API
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt_text},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=20000
            )

            # 获取模型回复
            analysis_result = response.choices[0].message.content

            # 添加到结果列表
            results.append({
                "image_index": idx,
                "filename": filename,
                "analysis_result": analysis_result
            })

            print(f"Processed image {idx}: {filename}")

        except Exception as e:
            print(f"Error processing image {idx} ({filename}): {str(e)}")
            results.append({
                "image_index": idx,
                "filename": filename,
                "analysis_result": f"ERROR: {str(e)}"
            })

    # 保存结果到Excel
    if results:
        folder_path_excel = os.path.dirname(output_excel)
        os.makedirs(folder_path_excel, exist_ok=True)
        df = pd.DataFrame(results)
        df.to_excel(output_excel, index=False)
        print(f"Results saved to {output_excel}")
    else:
        print("No results to save")


def encode_image(image_path):
    """将图片编码为base64字符串"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


# 使用示例
if __name__ == "__main__":
    # 配置参数
    config = {
        "folder_path": "D:/Data/仲裁卷宗-name/2024-1177",  # 替换为你的图片文件夹路径
        "output_excel": "D:/Data/仲裁卷宗-name/2024-1177-excel/v1.xlsx",
        "prompt_text": prompt.prompt_markdown,
        # "prompt_text": "按照表格形式输出所有数字",
        "openai_api_key": "token-abc123",
        "openai_api_base": "http://192.168.0.7:8005/v1",
        "image_indices": "21-56",  # 支持多种格式
        "model": "MiniCPM-V-2_6"
        # "model": "Qwen2.5-VL-7B-Instruct"
    }

    # 执行处理
    process_images_to_excel(**config)