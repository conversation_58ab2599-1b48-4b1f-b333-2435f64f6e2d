<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Viewer - Standalone</title>
    <script src="https://unpkg.com/marked@9.1.6/marked.min.js"></script>
    <script src="https://unpkg.com/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://unpkg.com/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link href="https://unpkg.com/prismjs@1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="https://unpkg.com/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" id="prism-theme">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        body.light {
            background-color: #ffffff;
            color: #333333;
        }

        body.dark {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            border-bottom: 1px solid;
            flex-shrink: 0;
        }

        .light .header {
            border-bottom-color: #e0e0e0;
            background-color: #f8f9fa;
        }

        .dark .header {
            border-bottom-color: #333333;
            background-color: #2d2d2d;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .toolbar {
            display: flex;
            gap: 0.5rem;
        }

        .button {
            padding: 0.5rem 1rem;
            border: 1px solid;
            border-radius: 6px;
            background: none;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .light .button {
            border-color: #d0d7de;
            color: #24292f;
        }

        .light .button:hover {
            background-color: #f3f4f6;
            border-color: #8c959f;
        }

        .dark .button {
            border-color: #30363d;
            color: #f0f6fc;
        }

        .dark .button:hover {
            background-color: #21262d;
            border-color: #8b949e;
        }

        .main {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .editor-panel,
        .preview-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .editor-panel {
            border-right: 1px solid;
        }

        .light .editor-panel {
            border-right-color: #e0e0e0;
        }

        .dark .editor-panel {
            border-right-color: #333333;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e0e0e0;
            background-color: #f8f9fa;
            flex-shrink: 0;
        }

        .panel-header h3 {
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
        }

        .char-count {
            font-size: 0.75rem;
            color: #6c757d;
        }

        .editor {
            flex: 1;
            padding: 1rem;
            border: none;
            outline: none;
            resize: none;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .light .editor {
            background-color: #ffffff;
            color: #24292f;
        }

        .dark .editor {
            background-color: #0d1117;
            color: #f0f6fc;
        }

        .preview {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .light .preview {
            background-color: #ffffff;
            color: #24292f;
        }

        .dark .preview {
            background-color: #0d1117;
            color: #f0f6fc;
        }

        /* Markdown styling */
        .preview h1, .preview h2, .preview h3, .preview h4, .preview h5, .preview h6 {
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
            line-height: 1.25;
        }

        .preview h1 {
            font-size: 2rem;
            border-bottom: 1px solid #d0d7de;
            padding-bottom: 0.3rem;
        }

        .preview h2 {
            font-size: 1.5rem;
            border-bottom: 1px solid #d0d7de;
            padding-bottom: 0.3rem;
        }

        .preview p {
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .preview ul, .preview ol {
            margin-bottom: 1rem;
            padding-left: 2rem;
        }

        .preview blockquote {
            margin: 1rem 0;
            padding: 0 1rem;
            border-left: 4px solid #d0d7de;
            background-color: #f6f8fa;
        }

        .preview code {
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(175, 184, 193, 0.2);
            border-radius: 6px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        }

        .preview pre {
            margin-bottom: 1rem;
            overflow-x: auto;
            border-radius: 6px;
        }

        .preview table {
            border-collapse: collapse;
            margin-bottom: 1rem;
            width: 100%;
        }

        .preview th, .preview td {
            padding: 6px 13px;
            border: 1px solid #d0d7de;
        }

        .preview th {
            font-weight: 600;
            background-color: #f6f8fa;
        }

        @media (max-width: 768px) {
            .main {
                flex-direction: column;
            }
            
            .editor-panel {
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }
            
            .header {
                padding: 0.75rem 1rem;
            }
        }
    </style>
</head>
<body class="light">
    <header class="header">
        <h1>📝 Markdown Viewer</h1>
        <div class="toolbar">
            <button class="button" onclick="loadExample()">Load Example</button>
            <button class="button" onclick="clearContent()">Clear</button>
            <button class="button" onclick="toggleTheme()">🌙</button>
        </div>
    </header>
    
    <main class="main">
        <div class="editor-panel">
            <div class="panel-header">
                <h3>📝 Editor</h3>
                <span class="char-count" id="charCount">0 characters</span>
            </div>
            <textarea 
                class="editor" 
                id="editor" 
                placeholder="Type your markdown here..."
                spellcheck="false"
            ></textarea>
        </div>
        <div class="preview-panel">
            <div class="panel-header">
                <h3>👁️ Preview</h3>
            </div>
            <div class="preview" id="preview">
                <em>Nothing to preview yet. Start typing in the editor!</em>
            </div>
        </div>
    </main>

    <script>
        const editor = document.getElementById('editor');
        const preview = document.getElementById('preview');
        const charCount = document.getElementById('charCount');
        const themeButton = document.querySelector('.toolbar .button:last-child');
        
        let isDark = false;
        const STORAGE_KEY = 'markdown-content';

        const defaultMarkdown = `# Welcome to Markdown Viewer

This is a **live markdown editor** with real-time preview.

## Features

- ✅ Real-time preview
- ✅ Syntax highlighting for code blocks
- ✅ Local storage (your content is saved automatically)
- ✅ Responsive design

## Code Example

\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
}

greet('World');
\`\`\`

## Table Example

| Feature | Status |
|---------|--------|
| Markdown parsing | ✅ |
| Code highlighting | ✅ |
| Tables | ✅ |

> **Tip**: Start typing in the left panel to see the magic happen!
`;

        // Configure marked for GitHub Flavored Markdown
        marked.setOptions({
            gfm: true,
            breaks: true,
            highlight: function(code, lang) {
                if (lang && Prism.languages[lang]) {
                    return Prism.highlight(code, Prism.languages[lang], lang);
                }
                return code;
            }
        });

        function updatePreview() {
            const markdown = editor.value;
            const html = marked.parse(markdown);
            preview.innerHTML = html || '<em>Nothing to preview yet. Start typing in the editor!</em>';
            
            // Update character count
            charCount.textContent = `${markdown.length} characters`;
            
            // Save to localStorage
            localStorage.setItem(STORAGE_KEY, markdown);
            
            // Re-highlight code blocks
            Prism.highlightAllUnder(preview);
        }

        function loadExample() {
            editor.value = defaultMarkdown;
            updatePreview();
        }

        function clearContent() {
            editor.value = '';
            updatePreview();
            localStorage.removeItem(STORAGE_KEY);
        }

        function toggleTheme() {
            isDark = !isDark;
            document.body.className = isDark ? 'dark' : 'light';
            themeButton.textContent = isDark ? '☀️' : '🌙';
            
            // Switch Prism theme
            const prismTheme = document.getElementById('prism-theme');
            prismTheme.href = isDark 
                ? 'https://unpkg.com/prismjs@1.29.0/themes/prism-tomorrow.min.css'
                : 'https://unpkg.com/prismjs@1.29.0/themes/prism.min.css';
        }

        // Load saved content on page load
        window.addEventListener('load', () => {
            const saved = localStorage.getItem(STORAGE_KEY);
            if (saved) {
                editor.value = saved;
            } else {
                editor.value = defaultMarkdown;
            }
            updatePreview();
        });

        // Update preview on input
        editor.addEventListener('input', updatePreview);
    </script>
</body>
</html>
