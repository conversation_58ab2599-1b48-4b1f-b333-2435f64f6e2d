export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#b7a68e url(./brown-papersq.png)",
    "color": "#363c69"
  },
  "hljs-keyword": {
    "color": "#005599",
    "fontWeight": "bold"
  },
  "hljs-selector-tag": {
    "color": "#005599",
    "fontWeight": "bold"
  },
  "hljs-literal": {
    "color": "#005599",
    "fontWeight": "bold"
  },
  "hljs-subst": {
    "color": "#363c69"
  },
  "hljs-string": {
    "color": "#2c009f"
  },
  "hljs-title": {
    "color": "#2c009f",
    "fontWeight": "bold"
  },
  "hljs-section": {
    "color": "#2c009f",
    "fontWeight": "bold"
  },
  "hljs-type": {
    "color": "#2c009f",
    "fontWeight": "bold"
  },
  "hljs-attribute": {
    "color": "#2c009f"
  },
  "hljs-symbol": {
    "color": "#2c009f"
  },
  "hljs-bullet": {
    "color": "#2c009f"
  },
  "hljs-built_in": {
    "color": "#2c009f"
  },
  "hljs-addition": {
    "color": "#2c009f"
  },
  "hljs-variable": {
    "color": "#2c009f"
  },
  "hljs-template-tag": {
    "color": "#2c009f"
  },
  "hljs-template-variable": {
    "color": "#2c009f"
  },
  "hljs-link": {
    "color": "#2c009f"
  },
  "hljs-name": {
    "color": "#2c009f",
    "fontWeight": "bold"
  },
  "hljs-comment": {
    "color": "#802022"
  },
  "hljs-quote": {
    "color": "#802022"
  },
  "hljs-meta": {
    "color": "#802022"
  },
  "hljs-deletion": {
    "color": "#802022"
  },
  "hljs-doctag": {
    "fontWeight": "bold"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  }
};