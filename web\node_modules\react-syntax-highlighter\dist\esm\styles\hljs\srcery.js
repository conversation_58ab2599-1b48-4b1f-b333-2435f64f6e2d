export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#1C1B19",
    "color": "#FCE8C3"
  },
  "hljs-strong": {
    "color": "#918175"
  },
  "hljs-emphasis": {
    "color": "#918175",
    "fontStyle": "italic"
  },
  "hljs-bullet": {
    "color": "#FF5C8F"
  },
  "hljs-quote": {
    "color": "#FF5C8F"
  },
  "hljs-link": {
    "color": "#FF5C8F"
  },
  "hljs-number": {
    "color": "#FF5C8F"
  },
  "hljs-regexp": {
    "color": "#FF5C8F"
  },
  "hljs-literal": {
    "color": "#FF5C8F"
  },
  "hljs-code": {
    "color": "#68A8E4"
  },
  "hljs-selector-class": {
    "color": "#68A8E4"
  },
  "hljs-keyword": {
    "color": "#EF2F27"
  },
  "hljs-selector-tag": {
    "color": "#EF2F27"
  },
  "hljs-section": {
    "color": "#EF2F27"
  },
  "hljs-attribute": {
    "color": "#EF2F27"
  },
  "hljs-variable": {
    "color": "#EF2F27"
  },
  "hljs-name": {
    "color": "#FBB829"
  },
  "hljs-title": {
    "color": "#FBB829"
  },
  "hljs-type": {
    "color": "#0AAEB3"
  },
  "hljs-params": {
    "color": "#0AAEB3"
  },
  "hljs-string": {
    "color": "#98BC37"
  },
  "hljs-subst": {
    "color": "#C07ABE"
  },
  "hljs-built_in": {
    "color": "#C07ABE"
  },
  "hljs-builtin-name": {
    "color": "#C07ABE"
  },
  "hljs-symbol": {
    "color": "#C07ABE"
  },
  "hljs-selector-id": {
    "color": "#C07ABE"
  },
  "hljs-selector-attr": {
    "color": "#C07ABE"
  },
  "hljs-selector-pseudo": {
    "color": "#C07ABE"
  },
  "hljs-template-tag": {
    "color": "#C07ABE"
  },
  "hljs-template-variable": {
    "color": "#C07ABE"
  },
  "hljs-addition": {
    "color": "#C07ABE"
  },
  "hljs-comment": {
    "color": "#918175"
  },
  "hljs-deletion": {
    "color": "#918175"
  },
  "hljs-meta": {
    "color": "#918175"
  }
};