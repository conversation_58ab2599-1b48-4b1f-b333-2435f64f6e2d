import os
from typing import List
from pdf2image import convert_from_path
from pdf2image.exceptions import PDFInfoNotInstalledError, PDFPageCountError, PDFSyntaxError


def pdf_to_images(
        pdf_path: str,
        output_folder: str,
        dpi: int = 200,
        image_format: str = "png",
        prefix: str = "page",
        poppler_path: str = None
) -> List[str]:
    """
    使用pdf2image将PDF文件按页转换为高质量图片

    参数:
        pdf_path: PDF文件路径
        output_folder: 输出图片的文件夹路径
        dpi: 输出图片分辨率（默认200）
        image_format: 图片格式（'png', 'jpeg'等，默认png）
        prefix: 输出文件名前缀（默认'page'）
        poppler_path: poppler工具路径（Windows需要指定，Linux/macOS通常自动检测）

    返回:
        生成的图片路径列表

    异常:
        可能抛出PDFInfoNotInstalledError（未安装poppler）等异常
    """
    # 确保输出目录存在
    os.makedirs(output_folder, exist_ok=True)

    # 设置输出格式
    fmt = image_format.lower()
    if fmt == "jpg":
        fmt = "jpg"  # PIL使用jpeg格式名

    output_folder1 = "E" + output_folder[1:]
    os.makedirs(output_folder1, exist_ok=True)
    # 转换PDF为图像列表
    images = convert_from_path(
        pdf_path=pdf_path,
        dpi=dpi,
        output_folder=output_folder1,
        output_file=prefix,
        fmt=fmt,
        paths_only=False,  # 返回图像对象而不是路径
        poppler_path=poppler_path,
        thread_count=4,  # 使用多线程加速
        grayscale=False,
        use_pdftocairo=False,
        transparent=False
    )

    # 保存图像文件
    generated_files = []
    for i, image in enumerate(images):
        output_path = os.path.join(output_folder, f"{prefix}{i + 1}.{image_format.lower()}")

        # 保存图像，对JPEG设置质量
        save_args = {}
        if fmt == "jpeg":
            save_args["quality"] = 95

        image.save(output_path, **save_args)
        generated_files.append(output_path)

    return generated_files


# 使用示例
if __name__ == "__main__":
    try:
        # 示例用法
        pdf_path = "D:/Data/仲裁卷宗-name/2024-1177.pdf"
        output_dir = "D:/Data/仲裁卷宗-name/2024-1177"

        # 调用函数
        image_paths = pdf_to_images(
            pdf_path=pdf_path,
            output_folder=output_dir,
            dpi=100,
            image_format="jpg",
            prefix=""
            # poppler_path=poppler_path  # 取消注释并设置Windows路径
        )

        print(f"成功生成 {len(image_paths)} 张图片:")
        for path in image_paths:
            print(f" - {path}")

    except Exception as e:
        print(f"发生未知错误: {str(e)}")