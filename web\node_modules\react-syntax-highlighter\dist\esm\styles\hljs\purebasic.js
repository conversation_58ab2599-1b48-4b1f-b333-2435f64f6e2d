export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#FFFFDF",
    "color": "#000000"
  },
  "hljs-type": {
    "color": "#000000"
  },
  "hljs-function": {
    "color": "#000000"
  },
  "hljs-name": {
    "color": "#000000",
    "fontWeight": "bold"
  },
  "hljs-number": {
    "color": "#000000"
  },
  "hljs-attr": {
    "color": "#000000"
  },
  "hljs-params": {
    "color": "#000000"
  },
  "hljs-subst": {
    "color": "#000000"
  },
  "hljs-comment": {
    "color": "#00AAAA"
  },
  "hljs-regexp": {
    "color": "#00AAAA"
  },
  "hljs-section": {
    "color": "#00AAAA"
  },
  "hljs-selector-pseudo": {
    "color": "#00AAAA"
  },
  "hljs-addition": {
    "color": "#00AAAA"
  },
  "hljs-title": {
    "color": "#006666"
  },
  "hljs-tag": {
    "color": "#006666"
  },
  "hljs-variable": {
    "color": "#006666"
  },
  "hljs-code": {
    "color": "#006666"
  },
  "hljs-keyword": {
    "color": "#006666",
    "fontWeight": "bold"
  },
  "hljs-class": {
    "color": "#006666",
    "fontWeight": "bold"
  },
  "hljs-meta-keyword": {
    "color": "#006666",
    "fontWeight": "bold"
  },
  "hljs-selector-class": {
    "color": "#006666",
    "fontWeight": "bold"
  },
  "hljs-built_in": {
    "color": "#006666",
    "fontWeight": "bold"
  },
  "hljs-builtin-name": {
    "color": "#006666",
    "fontWeight": "bold"
  },
  "hljs-string": {
    "color": "#0080FF"
  },
  "hljs-selector-attr": {
    "color": "#0080FF"
  },
  "hljs-symbol": {
    "color": "#924B72"
  },
  "hljs-link": {
    "color": "#924B72"
  },
  "hljs-deletion": {
    "color": "#924B72"
  },
  "hljs-attribute": {
    "color": "#924B72"
  },
  "hljs-meta": {
    "color": "#924B72",
    "fontWeight": "bold"
  },
  "hljs-literal": {
    "color": "#924B72",
    "fontWeight": "bold"
  },
  "hljs-selector-id": {
    "color": "#924B72",
    "fontWeight": "bold"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  }
};