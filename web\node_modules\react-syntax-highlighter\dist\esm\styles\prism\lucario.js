export default {
  "code[class*=\"language-\"]": {
    "color": "#f8f8f2",
    "background": "none",
    "textShadow": "0 1px rgba(0, 0, 0, 0.3)",
    "fontFamily": "Monaco, Consolas, 'Andale Mono', 'Ubuntu Mono', monospace",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#f8f8f2",
    "background": "#263E52",
    "textShadow": "0 1px rgba(0, 0, 0, 0.3)",
    "fontFamily": "Monaco, Consolas, 'Andale Mono', 'Ubuntu Mono', monospace",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "borderRadius": "0.3em"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "background": "#263E52",
    "padding": ".1em",
    "borderRadius": ".3em",
    "whiteSpace": "normal"
  },
  "comment": {
    "color": "#5c98cd"
  },
  "prolog": {
    "color": "#5c98cd"
  },
  "doctype": {
    "color": "#5c98cd"
  },
  "cdata": {
    "color": "#5c98cd"
  },
  "punctuation": {
    "color": "#f8f8f2"
  },
  ".namespace": {
    "Opacity": ".7"
  },
  "property": {
    "color": "#F05E5D"
  },
  "tag": {
    "color": "#F05E5D"
  },
  "constant": {
    "color": "#F05E5D"
  },
  "symbol": {
    "color": "#F05E5D"
  },
  "deleted": {
    "color": "#F05E5D"
  },
  "boolean": {
    "color": "#BC94F9"
  },
  "number": {
    "color": "#BC94F9"
  },
  "selector": {
    "color": "#FCFCD6"
  },
  "attr-name": {
    "color": "#FCFCD6"
  },
  "string": {
    "color": "#FCFCD6"
  },
  "char": {
    "color": "#FCFCD6"
  },
  "builtin": {
    "color": "#FCFCD6"
  },
  "inserted": {
    "color": "#FCFCD6"
  },
  "operator": {
    "color": "#f8f8f2"
  },
  "entity": {
    "color": "#f8f8f2",
    "cursor": "help"
  },
  "url": {
    "color": "#f8f8f2"
  },
  ".language-css .token.string": {
    "color": "#f8f8f2"
  },
  ".style .token.string": {
    "color": "#f8f8f2"
  },
  "variable": {
    "color": "#f8f8f2"
  },
  "atrule": {
    "color": "#66D8EF"
  },
  "attr-value": {
    "color": "#66D8EF"
  },
  "function": {
    "color": "#66D8EF"
  },
  "class-name": {
    "color": "#66D8EF"
  },
  "keyword": {
    "color": "#6EB26E"
  },
  "regex": {
    "color": "#F05E5D"
  },
  "important": {
    "color": "#F05E5D",
    "fontWeight": "bold"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  }
};