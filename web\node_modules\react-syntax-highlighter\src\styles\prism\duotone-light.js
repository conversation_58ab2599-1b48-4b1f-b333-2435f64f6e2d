export default {
    "code[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#faf8f5",
        "color": "#728fcb"
    },
    "pre[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#faf8f5",
        "color": "#728fcb",
        "padding": "1em",
        "margin": ".5em 0",
        "overflow": "auto"
    },
    "pre > code[class*=\"language-\"]": {
        "fontSize": "1em"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#faf8f5"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#faf8f5"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#faf8f5"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#faf8f5"
    },
    "pre[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#faf8f5"
    },
    "pre[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#faf8f5"
    },
    "code[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#faf8f5"
    },
    "code[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#faf8f5"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "padding": ".1em",
        "borderRadius": ".3em"
    },
    "comment": {
        "color": "#b6ad9a"
    },
    "prolog": {
        "color": "#b6ad9a"
    },
    "doctype": {
        "color": "#b6ad9a"
    },
    "cdata": {
        "color": "#b6ad9a"
    },
    "punctuation": {
        "color": "#b6ad9a"
    },
    "namespace": {
        "Opacity": ".7"
    },
    "tag": {
        "color": "#063289"
    },
    "operator": {
        "color": "#063289"
    },
    "number": {
        "color": "#063289"
    },
    "property": {
        "color": "#b29762"
    },
    "function": {
        "color": "#b29762"
    },
    "tag-id": {
        "color": "#2d2006"
    },
    "selector": {
        "color": "#2d2006"
    },
    "atrule-id": {
        "color": "#2d2006"
    },
    "code.language-javascript": {
        "color": "#896724"
    },
    "attr-name": {
        "color": "#896724"
    },
    "code.language-css": {
        "color": "#728fcb"
    },
    "code.language-scss": {
        "color": "#728fcb"
    },
    "boolean": {
        "color": "#728fcb"
    },
    "string": {
        "color": "#728fcb"
    },
    "entity": {
        "color": "#728fcb",
        "cursor": "help"
    },
    "url": {
        "color": "#728fcb"
    },
    ".language-css .token.string": {
        "color": "#728fcb"
    },
    ".language-scss .token.string": {
        "color": "#728fcb"
    },
    ".style .token.string": {
        "color": "#728fcb"
    },
    "attr-value": {
        "color": "#728fcb"
    },
    "keyword": {
        "color": "#728fcb"
    },
    "control": {
        "color": "#728fcb"
    },
    "directive": {
        "color": "#728fcb"
    },
    "unit": {
        "color": "#728fcb"
    },
    "statement": {
        "color": "#728fcb"
    },
    "regex": {
        "color": "#728fcb"
    },
    "atrule": {
        "color": "#728fcb"
    },
    "placeholder": {
        "color": "#93abdc"
    },
    "variable": {
        "color": "#93abdc"
    },
    "deleted": {
        "textDecoration": "line-through"
    },
    "inserted": {
        "borderBottom": "1px dotted #2d2006",
        "textDecoration": "none"
    },
    "italic": {
        "fontStyle": "italic"
    },
    "important": {
        "fontWeight": "bold",
        "color": "#896724"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "pre > code.highlight": {
        "Outline": ".4em solid #896724",
        "OutlineOffset": ".4em"
    },
    ".line-numbers.line-numbers .line-numbers-rows": {
        "borderRightColor": "#ece8de"
    },
    ".line-numbers .line-numbers-rows > span:before": {
        "color": "#cdc4b1"
    },
    ".line-highlight.line-highlight": {
        "background": "linear-gradient(to right, rgba(45, 32, 6, 0.2) 70%, rgba(45, 32, 6, 0))"
    }
}