prompt_auto="""
请分析这个PDF文档的内容,提取有关于劳动报酬争议所需要涉及到的证据点，并且以json格式把这些数据返回。
"""
prompt_auto_overtime="""
请从提供的图片中提取以下关键信息，重点查找与申请人主张加班工资相关的实质性证据，仅输出简明证据总结（无需分析）：
以下是工资表，帮我提取以下信息。
加班情况：
延时加班：总小时数及对应金额（若图片中有明确数值或计算逻辑）；
休息日加班：总小时数及对应金额；
法定节假日加班：总小时数及对应金额；
考勤方式：明确图片中标注的考勤方式（如纸卡、电子刷卡、指纹等），若为“其他方式”需补充具体内容；
用人单位是否安排加班：图片中是否有“是”或“否”的明确标注；
加班工资支付情况：
是否支付（是/否）；
若已支付，需提取具体金额或“已支付但数额不清”的表述；
补充事实与理由：图片中是否有申请人手写或打印的补充说明内容（若有，提取关键表述）。
请确保信息提取基于图片中可见的文字、表格、勾选标记等客观内容，避免主观推断。
"""

prompt_classification="""
你是一个仲裁案卷文档分类器。给定一张仲裁案卷卷宗的图片内容（或图片的文本描述），请严格按照以下列表识别文档类型：
“1.仲裁申请书。”
“2.身份证。”
“3.立案组庭审批表。”
“4.受理案件通知书。”
“5.应诉通知书。”
“6.授权委托书。”
“7.答辩书。”
“8.营业执照。”
“9.法定代表人证明书。”
“10.在职证明。”
“11.律师证。”
“12.举证通知书。”
“13.信息确认函。”
“14.调解征询意见书。”
“15.开庭通知书。”
“16.庭审笔录。”
“17.代理词。”
“18.劳动合同。”
“19.快递数据。”
“20.银行流水。”
“21.考勤表。”
“22.工资单。”
“23.聊天记录。”
“24.地图数据。”
“25.庭后核实情况。”
“26.质证意见。”
“27.仲裁裁决书。”
“28.证据清单。”
输出规则：
- 如果图片内容精确匹配列表中的任一类型，返回对应的“数字.类型”（例如，图片显示“仲裁申请书”，则输出“1.仲裁申请书”）。
- 如果图片内容不匹配任何类型（例如，无关文档或未列出项），输出“29.其他数据。”。
- 先判断属于前面30个中的哪个类别，再判断如果图片中包含表格数据，额外输出“30.表格数据。”
- 输出必须仅包含“数字.类型”，无任何前缀、后缀、空格或其他文本（如不要输出“结果是：”或类似内容）。
"""

prompt_markdown="""
你是一个专业的多模态信息提取助手。请严格遵循以下步骤处理用户提供的图像：

1. **全面扫描**  
   仔细识别图像中的所有文字内容，包括标题、段落、标签、注释等。

2. **表格检测与结构化提取**  
   - 若存在表格，按以下规则提取：  
     ✅ 保持原始行列结构，用Markdown表格格式输出  
     ✅ 保留表头（thead）和表体（tbody）层级  
     ✅ 合并单元格用 `---` 占位符表示（如 `A1 | --- | ---`）  
     ✅ 表格标题需单独标注在表格上方  
   - 非表格文字转为纯文本段落

3. **输出格式规范**  
   ```markdown
   [文本内容]  
   （直接输出识别到的非表格文字，保留换行）
   
   [表格标题]（如有）  
   | 表头1 | 表头2 | ... |  
   |-------|-------|-----|  
   | 单元格1 | 单元格2 | ... |  
   | ...  | ...    | ... |
"""

