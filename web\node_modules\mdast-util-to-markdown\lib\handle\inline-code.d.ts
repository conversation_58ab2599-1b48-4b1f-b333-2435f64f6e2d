/**
 * @param {InlineCode} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @returns {string}
 */
export function inlineCode(node: InlineCode, _: Parents | undefined, state: State): string;
export namespace inlineCode {
    export { inlineCodePeek as peek };
}
import type { InlineCode } from 'mdast';
import type { Parents } from 'mdast';
import type { State } from 'mdast-util-to-markdown';
/**
 * @returns {string}
 */
declare function inlineCodePeek(): string;
export {};
//# sourceMappingURL=inline-code.d.ts.map