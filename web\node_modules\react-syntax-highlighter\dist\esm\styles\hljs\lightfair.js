export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#fff",
    "color": "#444"
  },
  "hljs-name": {
    "color": "#01a3a3",
    "fontWeight": "bold"
  },
  "hljs-tag": {
    "color": "#778899"
  },
  "hljs-meta": {
    "color": "#778899"
  },
  "hljs-subst": {
    "color": "#444"
  },
  "hljs-comment": {
    "color": "#888888"
  },
  "hljs-keyword": {
    "fontWeight": "bold"
  },
  "hljs-attribute": {
    "fontWeight": "bold"
  },
  "hljs-selector-tag": {
    "fontWeight": "bold"
  },
  "hljs-meta-keyword": {
    "fontWeight": "bold"
  },
  "hljs-doctag": {
    "fontWeight": "bold"
  },
  "hljs-type": {
    "color": "#4286f4"
  },
  "hljs-string": {
    "color": "#4286f4"
  },
  "hljs-number": {
    "color": "#4286f4"
  },
  "hljs-selector-id": {
    "color": "#4286f4"
  },
  "hljs-selector-class": {
    "color": "#4286f4"
  },
  "hljs-quote": {
    "color": "#4286f4"
  },
  "hljs-template-tag": {
    "color": "#4286f4"
  },
  "hljs-deletion": {
    "color": "#4286f4"
  },
  "hljs-title": {
    "color": "#4286f4",
    "fontWeight": "bold"
  },
  "hljs-section": {
    "color": "#4286f4",
    "fontWeight": "bold"
  },
  "hljs-regexp": {
    "color": "#BC6060"
  },
  "hljs-symbol": {
    "color": "#BC6060"
  },
  "hljs-variable": {
    "color": "#BC6060"
  },
  "hljs-template-variable": {
    "color": "#BC6060"
  },
  "hljs-link": {
    "color": "#BC6060"
  },
  "hljs-selector-attr": {
    "color": "#BC6060"
  },
  "hljs-selector-pseudo": {
    "color": "#BC6060"
  },
  "hljs-literal": {
    "color": "#62bcbc"
  },
  "hljs-built_in": {
    "color": "#25c6c6"
  },
  "hljs-bullet": {
    "color": "#25c6c6"
  },
  "hljs-code": {
    "color": "#25c6c6"
  },
  "hljs-addition": {
    "color": "#25c6c6"
  },
  "hljs-meta-string": {
    "color": "#4d99bf"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};